// Unified Dashboard JavaScript for Excalibur ERP
// Combines both provided scripts with optimizations and improvements

// Global State Management
const AppState = {
  currentTab: "of-en-cours",
  currentViewMode: "overview",
  filters: {
    dateDebut: null,
    dateFin: null,
    statutFilter: "",
    familleFilter: "",
    clientFilter: "",
    alertsOnly: false,
    prioriteFilter: "",
    secteurFilter: "",
    avancementFilter: "",
    retardFilter: false,
  },
  data: {
    ofEnCours: null,
    ofHistorique: null,
    ofCombined: null,
    alerts: null,
    kpis: null,
    filterOptions: null,
  },
  lastUpdate: null,
  isLoading: false,
};

// API Configuration
const API_BASE = "";
const API_ENDPOINTS = {
  config: "/api/config",
  dashboard: "/api/dashboard-data",
  kpis: "/api/kpis",
  filters: "/api/filters/options",
  alerts: "/api/alerts/",
  of: {
    en_cours: "/api/of/en_cours",
    histo: "/api/of/histo",
    all: "/api/of/all",
    current: "/api/of/current",
    filtered: "/api/of/filtered",
  },
  export: {
    csv: "/api/export/csv",
    excel: "/api/export/excel",
    txt: "/api/export/txt-resume",
  },
  health: {
    database: "/api/health/database",
  },
};

// Utility Functions
const Utils = {
  formatDate: (date) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString("fr-FR");
  },

  formatNumber: (num) => {
    if (num === null || num === undefined) return "0";
    return new Intl.NumberFormat("fr-FR").format(num);
  },

  formatPercentage: (num) => {
    if (num === null || num === undefined) return "0%";
    return `${parseFloat(num).toFixed(1)}%`;
  },

  showAlert: (message, type = "info") => {
    const alertElement = document.getElementById(`${type}Alert`);
    const messageElement = document.getElementById(`${type}Message`);

    if (alertElement && messageElement) {
      messageElement.textContent = message;
      alertElement.style.display = "block";

      if (type !== "error" && type !== "connection") {
        setTimeout(() => {
          alertElement.style.display = "none";
        }, 5000);
      }
    }
  },

  hideAllAlerts: () => {
    ["info", "error", "connection"].forEach((type) => {
      const alert = document.getElementById(`${type}Alert`);
      if (alert) alert.style.display = "none";
    });
  },

  setLoading: (isLoading) => {
    AppState.isLoading = isLoading;
    const loadingIndicator = document.getElementById("loadingIndicator");
    if (loadingIndicator) {
      loadingIndicator.style.display = isLoading ? "block" : "none";
    }
  },

  updateConnectionStatus: (isConnected) => {
    const connectedStatus = document.getElementById("connectionStatus");
    const errorStatus = document.getElementById("errorStatus");

    if (isConnected) {
      if (connectedStatus) connectedStatus.style.display = "inline-flex";
      if (errorStatus) errorStatus.style.display = "none";
    } else {
      if (connectedStatus) connectedStatus.style.display = "none";
      if (errorStatus) errorStatus.style.display = "inline-flex";
    }
  },

  getStatusClass: (status) => {
    const statusMap = {
      C: "primary", // En cours
      T: "success", // Terminé
      A: "warning", // Arrêté
      P: "info", // Planifié
      E: "danger", // Erreur
    };
    return statusMap[status] || "secondary";
  },

  getChargeBadgeClass: (percentage) => {
    const pct = parseFloat(percentage) || 0;
    if (pct > 100) return "danger";
    if (pct > 80) return "warning";
    if (pct > 60) return "info";
    return "success";
  },

  getPriorityBadgeClass: (priority) => {
    switch (priority) {
      case "URGENT":
        return "danger";
      case "PRIORITAIRE":
        return "warning";
      case "NORMAL":
        return "success";
      default:
        return "secondary";
    }
  },

  getPriorityRowClass: (priority) => {
    switch (priority) {
      case "URGENT":
        return "table-danger";
      case "PRIORITAIRE":
        return "table-warning";
      default:
        return "";
    }
  },
};

// API Service
const ApiService = {
  async makeRequest(url, options = {}) {
    try {
      const response = await fetch(API_BASE + url, {
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      Utils.updateConnectionStatus(true);
      return data;
    } catch (error) {
      console.error("API Request failed:", error);
      Utils.updateConnectionStatus(false);
      throw error;
    }
  },

  async getDashboardData(filters = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== "") {
        params.append(key, value);
      }
    });

    const url = `${API_ENDPOINTS.dashboard}?${params.toString()}`;
    return await this.makeRequest(url);
  },

  async getKPIs(filters = {}) {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== "") {
        params.append(key, value);
      }
    });

    const url = `${API_ENDPOINTS.kpis}?${params.toString()}`;
    return await this.makeRequest(url);
  },

  async getFilterOptions() {
    return await this.makeRequest(API_ENDPOINTS.filters);
  },

  async getAlerts() {
    return await this.makeRequest(API_ENDPOINTS.alerts);
  },

  async getOFData(type = "all", filters = {}) {
    const endpoint = API_ENDPOINTS.of[type] || API_ENDPOINTS.of.all;
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== "") {
        params.append(key, value);
      }
    });

    const url = `${endpoint}?${params.toString()}`;
    return await this.makeRequest(url);
  },
};

// Filter Management
const FilterManager = {
  init() {
    this.setupEventListeners();
    this.loadFilterOptions();
    this.setDefaultDates();
  },

  setupEventListeners() {
    // Date filters
    document
      .getElementById("dateDebut")
      ?.addEventListener("change", this.onFilterChange.bind(this));
    document
      .getElementById("dateFin")
      ?.addEventListener("change", this.onFilterChange.bind(this));

    // Dropdown filters
    document
      .getElementById("statutFilter")
      ?.addEventListener("change", this.onFilterChange.bind(this));
    document
      .getElementById("familleFilter")
      ?.addEventListener("change", this.onFilterChange.bind(this));
    document
      .getElementById("clientFilter")
      ?.addEventListener("change", this.onFilterChange.bind(this));

    // Advanced filters
    document
      .getElementById("prioriteFilter")
      ?.addEventListener("change", this.onFilterChange.bind(this));
    document
      .getElementById("secteurFilter")
      ?.addEventListener("change", this.onFilterChange.bind(this));
    document
      .getElementById("avancementFilter")
      ?.addEventListener("change", this.onFilterChange.bind(this));
    document
      .getElementById("retardFilter")
      ?.addEventListener("change", this.onFilterChange.bind(this));

    // Date range toggle
    const enableDateRange = document.getElementById("enableDateRange");
    const dateRangeContainer = document.getElementById("dateRangeContainer");

    if (enableDateRange && dateRangeContainer) {
      enableDateRange.addEventListener("change", function () {
        if (this.checked) {
          dateRangeContainer.style.display = "block";
          FilterManager.setDefaultDates();
        } else {
          dateRangeContainer.style.display = "none";
          document.getElementById("dateDebut").value = "";
          document.getElementById("dateFin").value = "";
          AppState.filters.dateDebut = null;
          AppState.filters.dateFin = null;
          DataManager.refreshCurrentView();
        }
      });
    }
  },

  async loadFilterOptions() {
    try {
      const response = await ApiService.getFilterOptions();
      if (response.success) {
        AppState.data.filterOptions = response.data.options || response.data;
        this.populateFilterDropdowns(response.data);
      }
    } catch (error) {
      console.error("Failed to load filter options:", error);
    }
  },

  populateFilterDropdowns(options) {
    // Populate status filter
    const statutSelect = document.getElementById("statutFilter");
    if (statutSelect && options.statuts) {
      options.statuts.forEach((status) => {
        const option = document.createElement("option");
        option.value = status;
        option.textContent = status;
        statutSelect.appendChild(option);
      });
    }

    // Populate family filter
    const familleSelect = document.getElementById("familleFilter");
    if (familleSelect && options.familles) {
      familleSelect.innerHTML = '<option value="">Toutes les familles</option>';
      options.familles.forEach((famille) => {
        const option = document.createElement("option");
        option.value = famille;
        option.textContent = famille;
        familleSelect.appendChild(option);
      });
    }

    // Populate client filter
    const clientSelect = document.getElementById("clientFilter");
    if (clientSelect && options.clients) {
      clientSelect.innerHTML = '<option value="">Tous les clients</option>';
      options.clients.forEach((client) => {
        const option = document.createElement("option");
        option.value = client;
        option.textContent = client;
        clientSelect.appendChild(option);
      });
    }

    // Populate sector filter
    const secteurSelect = document.getElementById("secteurFilter");
    if (secteurSelect && options.secteurs) {
      secteurSelect.innerHTML = '<option value="">Tous les secteurs</option>';
      options.secteurs.forEach((secteur) => {
        const option = document.createElement("option");
        option.value = secteur;
        option.textContent = secteur;
        secteurSelect.appendChild(option);
      });
    }
  },

  setDefaultDates() {
    const today = new Date();
    const threeMonthsAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);

    const dateDebut = document.getElementById("dateDebut");
    const dateFin = document.getElementById("dateFin");

    if (dateDebut) {
      dateDebut.value = threeMonthsAgo.toISOString().split("T")[0];
      AppState.filters.dateDebut = dateDebut.value;
    }

    if (dateFin) {
      dateFin.value = today.toISOString().split("T")[0];
      AppState.filters.dateFin = dateFin.value;
    }
  },

  onFilterChange() {
    this.updateFiltersFromUI();
    DataManager.refreshCurrentView();
  },

  updateFiltersFromUI() {
    AppState.filters.dateDebut =
      document.getElementById("dateDebut")?.value || null;
    AppState.filters.dateFin =
      document.getElementById("dateFin")?.value || null;
    AppState.filters.statutFilter =
      document.getElementById("statutFilter")?.value || "";
    AppState.filters.familleFilter =
      document.getElementById("familleFilter")?.value || "";
    AppState.filters.clientFilter =
      document.getElementById("clientFilter")?.value || "";
    AppState.filters.prioriteFilter =
      document.getElementById("prioriteFilter")?.value || "";
    AppState.filters.secteurFilter =
      document.getElementById("secteurFilter")?.value || "";
    AppState.filters.avancementFilter =
      document.getElementById("avancementFilter")?.value || "";
    AppState.filters.retardFilter =
      document.getElementById("retardFilter")?.checked || false;
  },

  getActiveFilters() {
    return { ...AppState.filters };
  },

  clearAllFilters() {
    document.getElementById("dateDebut").value = "";
    document.getElementById("dateFin").value = "";
    document.getElementById("statutFilter").value = "";
    document.getElementById("familleFilter").value = "";
    document.getElementById("clientFilter").value = "";
    document.getElementById("prioriteFilter").value = "";
    document.getElementById("secteurFilter").value = "";
    document.getElementById("avancementFilter").value = "";
    document.getElementById("retardFilter").checked = false;

    AppState.filters = {
      dateDebut: null,
      dateFin: null,
      statutFilter: "",
      familleFilter: "",
      clientFilter: "",
      alertsOnly: false,
      prioriteFilter: "",
      secteurFilter: "",
      avancementFilter: "",
      retardFilter: false,
    };

    this.setDefaultDates();
    DataManager.refreshCurrentView();
  },
};

// Data Management
const DataManager = {
  async loadInitialData() {
    Utils.setLoading(true);

    try {
      await this.loadConfiguration();
      await this.loadOFEnCoursData();
      await this.loadAlerts();
      Utils.showAlert("Données chargées avec succès", "connection");
      AppState.lastUpdate = new Date();
      this.updateLastRefreshTime();
    } catch (error) {
      console.error("Failed to load initial data:", error);
      Utils.showAlert("Erreur lors du chargement des données", "error");
    } finally {
      Utils.setLoading(false);
    }
  },

  async loadConfiguration() {
    try {
      const response = await ApiService.makeRequest(API_ENDPOINTS.config);
      if (response.success) {
        console.log("Configuration loaded:", response.data);
      }
    } catch (error) {
      console.warn("Failed to load configuration:", error);
    }
  },

  async loadOFEnCoursData() {
    try {
      const filters = FilterManager.getActiveFilters();
      const response = await ApiService.getOFData("en_cours", filters);

      if (response.success) {
        AppState.data.ofEnCours = response.data;
        this.renderOFTable("enCours", response.data);
        this.renderKPICards("enCours", response.data);
      }
    } catch (error) {
      console.error("Failed to load OF En Cours data:", error);
      this.renderTableError("enCours", error.message);
    }
  },

  async loadOFHistoriqueData() {
    try {
      const filters = FilterManager.getActiveFilters();
      const response = await ApiService.getOFData("histo", filters);

      if (response.success) {
        AppState.data.ofHistorique = response.data;
        this.renderOFTable("historique", response.data);
        this.renderKPICards("historique", response.data);
      }
    } catch (error) {
      console.error("Failed to load OF Historique data:", error);
      this.renderTableError("historique", error.message);
    }
  },

  async loadOFCombinedData() {
    try {
      const filters = FilterManager.getActiveFilters();
      const response = await ApiService.getOFData("all", filters);

      if (response.success) {
        AppState.data.ofCombined = response.data;
        this.renderOFTable("combined", response.data);
        this.renderKPICards("combined", response.data);
      }
    } catch (error) {
      console.error("Failed to load OF Combined data:", error);
      this.renderTableError("combined", error.message);
    }
  },

  async loadAlerts() {
    try {
      const response = await ApiService.getAlerts();
      if (response.success) {
        AppState.data.alerts = response.data;
        this.renderAlerts();
      }
    } catch (error) {
      console.error("Failed to load alerts:", error);
    }
  },

  renderKPICards(type, data) {
    const containerId = `${type}KpiCards`;
    const container = document.getElementById(containerId);
    if (!container || !data) return;

    // Calculate KPIs based on data
    const totalOrders = Array.isArray(data) ? data.length : 0;
    let activeOrders = 0;
    let completedOrders = 0;
    let avgProgress = 0;
    let alertsCount = 0;

    if (Array.isArray(data) && data.length > 0) {
      if (type === "historique") {
        completedOrders = totalOrders;
      } else {
        activeOrders = data.filter((item) => item.STATUT === "C").length;
        completedOrders = data.filter((item) => item.STATUT === "T").length;
        alertsCount = data.filter((item) => item.Alerte_temps).length;
      }

      avgProgress =
        data.reduce(
          (sum, item) => sum + (parseFloat(item.Avancement_PROD) || 0),
          0
        ) / data.length;
    }

    const completionRate =
      totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;

    container.innerHTML = `
      <div class="row g-3">
        <div class="col-md-3">
          <div class="kpi-card primary">
            <div class="kpi-value">${Utils.formatNumber(totalOrders)}</div>
            <div class="kpi-label">Total Commandes</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="kpi-card ${
            type === "historique" ? "success" : "warning"
          }">
            <div class="kpi-value">${Utils.formatNumber(
              type === "historique" ? totalOrders : activeOrders
            )}</div>
            <div class="kpi-label">${
              type === "historique" ? "Terminées" : "En Cours"
            }</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="kpi-card info">
            <div class="kpi-value">${Utils.formatPercentage(
              completionRate
            )}</div>
            <div class="kpi-label">Taux Completion</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="kpi-card ${alertsCount > 0 ? "danger" : "success"}">
            <div class="kpi-value">${Utils.formatPercentage(
              avgProgress * 100
            )}</div>
            <div class="kpi-label">Avancement Moyen</div>
          </div>
        </div>
      </div>
    `;
  },

  renderOFTable(type, data) {
    const headerId = `${type}Header`;
    const bodyId = `${type}Body`;
    const headerElement = document.getElementById(headerId);
    const bodyElement = document.getElementById(bodyId);

    if (!headerElement || !bodyElement) return;

    // Define columns
    const columns = [
      { key: "NUMERO_OFDA", label: "N° OF" },
      { key: "PRODUIT", label: "Produit" },
      { key: "DESIGNATION", label: "Désignation" },
      { key: "STATUT", label: "Statut" },
      { key: "CLIENT", label: "Client" },
      { key: "FAMILLE_TECHNIQUE", label: "Famille" },
      { key: "LANCEMENT_AU_PLUS_TARD", label: "Lancement" },
      { key: "QUANTITE_DEMANDEE", label: "Qté Demandée" },
      { key: "CUMUL_ENTREES", label: "Qté Produite" },
      { key: "Avancement_PROD", label: "Avancement" },
    ];

    // Add source column for combined view
    if (type === "combined") {
      columns.push({ key: "SOURCE_TABLE", label: "Source" });
    }

    // Render header
    headerElement.innerHTML = `
      <tr>
        ${columns.map((col) => `<th>${col.label}</th>`).join("")}
        <th>Actions</th>
      </tr>
    `;

    // Render body
    if (!data || !Array.isArray(data) || data.length === 0) {
      bodyElement.innerHTML = `
        <tr>
          <td colspan="${columns.length + 1}" class="text-center text-muted">
            <i class="fas fa-spinner fa-spin"></i> Aucune donnée disponible
          </td>
        </tr>
      `;
      return;
    }

    const rowsHtml = data
      .slice(0, 100)
      .map((row) => {
        const statusClass = Utils.getStatusClass(row.STATUT);
        const progressPercentage = Math.round(
          (parseFloat(row.Avancement_PROD) || 0) * 100
        );

        return `
        <tr>
          ${columns
            .map((col) => {
              let value = row[col.key];

              if (col.key === "Avancement_PROD") {
                value = `
                <div class="progress" style="height: 20px;">
                  <div class="progress-bar bg-${statusClass}"
                       role="progressbar"
                       style="width: ${progressPercentage}%"
                       aria-valuenow="${progressPercentage}"
                       aria-valuemin="0"
                       aria-valuemax="100">
                    ${progressPercentage}%
                  </div>
                </div>
              `;
              } else if (col.key === "STATUT") {
                value = `<span class="badge bg-${statusClass}">${
                  value || "N/A"
                }</span>`;
              } else if (col.key === "LANCEMENT_AU_PLUS_TARD") {
                value = Utils.formatDate(value);
              } else if (col.key === "SOURCE_TABLE") {
                value = value === "OF_DA" ? "En Cours" : "Historique";
              } else if (typeof value === "number") {
                value = Utils.formatNumber(value);
              } else {
                value = value || "N/A";
              }

              return `<td>${value}</td>`;
            })
            .join("")}
          <td>
            <div class="btn-group btn-group-sm" role="group">
              <button class="btn btn-outline-primary btn-sm"
                      onclick="DataManager.showOrderDetails('${
                        row.NUMERO_OFDA
                      }')"
                      title="Voir détails">
                <i class="fas fa-eye"></i>
              </button>
              <button class="btn btn-outline-secondary btn-sm"
                      onclick="ExportManager.exportSingleOrder('${
                        row.NUMERO_OFDA
                      }')"
                      title="Exporter">
                <i class="fas fa-download"></i>
              </button>
            </div>
          </td>
        </tr>
      `;
      })
      .join("");

    bodyElement.innerHTML = rowsHtml;
  },

  renderTableError(type, errorMessage) {
    const bodyId = `${type}Body`;
    const bodyElement = document.getElementById(bodyId);

    if (bodyElement) {
      bodyElement.innerHTML = `
        <tr>
          <td colspan="10" class="text-center">
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erreur: ${errorMessage}
              <button class="btn btn-sm btn-outline-danger ms-2"
                      onclick="DataManager.refreshCurrentView()">
                Réessayer
              </button>
            </div>
          </td>
        </tr>
      `;
    }
  },

  renderAlerts() {
    const alertsContainer = document.getElementById("alertsContainer");
    if (!alertsContainer) return;

    const alerts = AppState.data.alerts || [];

    if (alerts.length === 0) {
      alertsContainer.innerHTML = `
        <div class="text-center text-muted">
          <i class="fas fa-check-circle text-success me-2"></i>
          Aucune alerte active
        </div>
      `;
      return;
    }

    const alertsHtml = alerts
      .slice(0, 5)
      .map((alert) => {
        const severityClass =
          {
            critical: "danger",
            high: "warning",
            medium: "info",
            low: "secondary",
          }[alert.severity] || "secondary";

        return `
        <div class="alert alert-${severityClass} alert-dismissible fade show mb-2" role="alert">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <strong>${alert.title}</strong>
              <div class="small">${alert.message}</div>
              <div class="small text-muted">
                ${Utils.formatDate(alert.created_at)} - ${alert.category}
              </div>
            </div>
            <div class="ms-3">
              <span class="badge bg-${severityClass}">${alert.severity.toUpperCase()}</span>
            </div>
          </div>
        </div>
      `;
      })
      .join("");

    alertsContainer.innerHTML = alertsHtml;
  },

  updateDataSummary() {
    const dataCount = document.getElementById("dataCount");
    const lastRefresh = document.getElementById("lastRefresh");
    const systemStatus = document.getElementById("systemStatus");
    const responseTime = document.getElementById("responseTime");

    if (dataCount) {
      const totalRecords =
        (AppState.data.ofEnCours?.length || 0) +
        (AppState.data.ofHistorique?.length || 0) +
        (AppState.data.ofCombined?.length || 0);
      dataCount.textContent = Utils.formatNumber(totalRecords);
    }

    if (lastRefresh && AppState.lastUpdate) {
      lastRefresh.textContent = Utils.formatDate(AppState.lastUpdate);
    }

    if (systemStatus) {
      systemStatus.textContent = AppState.isLoading
        ? "Chargement..."
        : "Opérationnel";
    }

    if (responseTime) {
      responseTime.textContent = "< 1s"; // Simulated
    }
  },

  updateLastRefreshTime() {
    const lastUpdateElement = document.getElementById("lastUpdateTime");
    if (lastUpdateElement && AppState.lastUpdate) {
      lastUpdateElement.textContent = `Dernière mise à jour: ${AppState.lastUpdate.toLocaleTimeString(
        "fr-FR"
      )}`;
    }
  },

  showOrderDetails(orderId) {
    console.log("Show details for order:", orderId);
    Utils.showAlert(`Détails pour la commande ${orderId}`, "info");
  },

  async refreshCurrentView() {
    Utils.setLoading(true);

    try {
      switch (AppState.currentTab) {
        case "of-en-cours":
          await this.loadOFEnCoursData();
          break;
        case "of-historique":
          await this.loadOFHistoriqueData();
          break;
        case "of-combined":
          await this.loadOFCombinedData();
          break;
      }

      AppState.lastUpdate = new Date();
      this.updateLastRefreshTime();
      Utils.showAlert("Données actualisées", "connection");
    } catch (error) {
      console.error("Failed to refresh data:", error);
      Utils.showAlert("Erreur lors de l'actualisation", "error");
    } finally {
      Utils.setLoading(false);
    }
  },
};

// Tab Management
const TabManager = {
  currentTab: "of-en-cours",
  tabConfigs: {
    "of-en-cours": {
      endpoint: API_ENDPOINTS.of.en_cours,
      kpiContainerId: "enCoursKpiCards",
      tableId: "enCoursTable",
      headerId: "enCoursHeader",
      bodyId: "enCoursBody",
      columns: [
        { field: "NUMERO_OFDA", label: "N° OF" },
        { field: "PRODUIT", label: "Produit" },
        { field: "DESIGNATION", label: "Désignation" },
        { field: "CLIENT", label: "Client" },
        { field: "FAMILLE_TECHNIQUE", label: "Famille" },
        { field: "STATUT", label: "Statut" },
        { field: "Avancement_PROD", label: "Avanc. Prod" },
        { field: "Avancement_temps", label: "Avanc. Temps" },
        { field: "EFFICACITE", label: "Efficacité" },
      ],
    },
    "of-historique": {
      endpoint: API_ENDPOINTS.of.histo,
      kpiContainerId: "historiqueKpiCards",
      tableId: "historiqueTable",
      headerId: "historiqueHeader",
      bodyId: "historiqueBody",
      columns: [
        { field: "NUMERO_OFDA", label: "N° OF" },
        { field: "PRODUIT", label: "Produit" },
        { field: "CLIENT", label: "Client" },
        { field: "DATE_FIN", label: "Date Fin" },
        { field: "DUREE_TOTALE", label: "Durée" },
        { field: "EFFICACITE", label: "Efficacité" },
      ],
    },
    "of-combined": {
      endpoint: API_ENDPOINTS.of.all,
      kpiContainerId: "combinedKpiCards",
      tableId: "combinedTable",
      headerId: "combinedHeader",
      bodyId: "combinedBody",
      columns: [
        { field: "NUMERO_OFDA", label: "N° OF" },
        { field: "PRODUIT", label: "Produit" },
        { field: "CLIENT", label: "Client" },
        { field: "STATUT", label: "Statut" },
        { field: "Avancement_PROD", label: "Avanc. Prod" },
        { field: "Avancement_temps", label: "Avanc. Temps" },
        { field: "EFFICACITE", label: "Efficacité" },
      ],
    },
  },

  async switchMainTab(tabId) {
    this.currentTab = tabId;
    await dataManager.refreshData();
  },

  getTabConfig() {
    return this.tabConfigs[this.currentTab];
  },
};

// Update dataManager with new methods
Object.assign(DataManager, {
  async updateUI(data) {
    const tabConfig = TabManager.getTabConfig();
    if (!tabConfig) return;

    // Update KPIs for current tab
    this.updateKPIs(data.kpis, tabConfig.kpiContainerId);

    // Update table for current tab
    this.updateTable(data.data, tabConfig);

    // Update filters if needed
    if (data.filters) {
      this.updateFilters(data.filters);
    }

    // Update system status
    this.updateSystemStatus(data.status);
  },

  updateKPIs(kpis, containerId) {
    const container = document.getElementById(containerId);
    if (!container || !kpis) return;

    container.innerHTML = uiComponents.renderKPIs(kpis);
  },

  updateTable(data, tabConfig) {
    if (!data || !tabConfig) return;

    // Update header
    const headerElement = document.getElementById(tabConfig.headerId);
    if (headerElement) {
      headerElement.innerHTML = this.generateTableHeader(tabConfig.columns);
    }

    // Update body
    const bodyElement = document.getElementById(tabConfig.bodyId);
    if (bodyElement) {
      bodyElement.innerHTML = this.generateTableRows(data, tabConfig.columns);
    }
  },

  generateTableHeader(columns) {
    return `<tr>${columns.map((col) => `<th>${col.label}</th>`).join("")}</tr>`;
  },

  generateTableRows(data, columns) {
    return data
      .map(
        (row) => `
            <tr>
                ${columns
                  .map((col) => {
                    const value = row[col.field];
                    if (col.field === "STATUT") {
                      return `<td>${uiComponents.createBadge(
                        value,
                        "status",
                        value
                      )}</td>`;
                    }
                    if (col.field.includes("Avancement")) {
                      return `<td>${Utils.formatPercentage(value)}</td>`;
                    }
                    if (col.field === "EFFICACITE") {
                      return `<td>${
                        value ? parseFloat(value).toFixed(2) : "-"
                      }</td>`;
                    }
                    return `<td>${value || "-"}</td>`;
                  })
                  .join("")}
            </tr>
        `
      )
      .join("");
  },

  updateFilters(filterData) {
    // Update famille filter
    const familleSelect = document.getElementById("familleFilter");
    if (familleSelect && filterData.familles) {
      this.populateSelect(
        familleSelect,
        filterData.familles,
        "Toutes les familles"
      );
    }

    // Update client filter
    const clientSelect = document.getElementById("clientFilter");
    if (clientSelect && filterData.clients) {
      this.populateSelect(clientSelect, filterData.clients, "Tous les clients");
    }

    // Update secteur filter
    const secteurSelect = document.getElementById("secteurFilter");
    if (secteurSelect && filterData.secteurs) {
      this.populateSelect(
        secteurSelect,
        filterData.secteurs,
        "Tous les secteurs"
      );
    }
  },

  populateSelect(selectElement, options, defaultLabel) {
    const currentValue = selectElement.value;
    selectElement.innerHTML = `<option value="">${defaultLabel}</option>`;
    options.forEach((option) => {
      const optElement = document.createElement("option");
      optElement.value = option;
      optElement.textContent = option;
      selectElement.appendChild(optElement);
    });
    if (currentValue && options.includes(currentValue)) {
      selectElement.value = currentValue;
    }
  },

  updateSystemStatus(status) {
    if (!status) return;

    document.getElementById("systemStatus").textContent = status.message || "-";
    document.getElementById("responseTime").textContent = status.responseTime
      ? `${status.responseTime}ms`
      : "-";
    document.getElementById("dataCount").textContent =
      status.totalRecords || "0";
    document.getElementById("lastRefresh").textContent =
      new Date().toLocaleTimeString();
  },
});

// Add alert management
const alertManager = {
  async refreshAlerts() {
    try {
      const response = await fetch(API_ENDPOINTS.backlog.urgent);
      if (!response.ok) throw new Error("Failed to fetch alerts");

      const data = await response.json();
      this.updateAlertsUI(data.data || []);
    } catch (error) {
      console.error("Error refreshing alerts:", error);
      Utils.showAlert("error", "Failed to refresh alerts");
    }
  },

  updateAlertsUI(alerts) {
    const container = document.getElementById("alertsContainer");
    if (!container) return;

    if (alerts.length === 0) {
      container.innerHTML =
        '<div class="alert alert-success">No alerts at this time</div>';
      return;
    }

    container.innerHTML = alerts
      .map(
        (alert) => `
            <div class="alert alert-${this.getAlertLevel(alert)}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${alert.NUMERO_OFDA}</strong> - ${alert.PRODUIT}
                        <br>
                        <small class="text-muted">
                            Client: ${alert.CLIENT} |
                            Retard: ${alert.RETARD_JOURS} jours
                        </small>
                    </div>
                    <div>
                        ${this.getAlertBadge(alert)}
                    </div>
                </div>
            </div>
        `
      )
      .join("");
  },

  getAlertLevel(alert) {
    if (alert.RETARD_JOURS > 30) return "danger";
    if (alert.RETARD_JOURS > 15) return "warning";
    return "info";
  },

  getAlertBadge(alert) {
    const priority = alert.PRIORITE || "NORMAL";
    return uiComponents.createBadge(priority, "priority", priority);
  },
};

// Initialize dashboard with enhanced functionality
document.addEventListener("DOMContentLoaded", function () {
  if (typeof Plotly === "undefined") {
    console.error("❌ Plotly.js failed to load");
    return;
  }

  console.log("✅ Plotly.js loaded successfully, version:", Plotly.BUILD);

  eventHandlers.setupEventListeners();
  DataManager.refreshData();
  alertManager.refreshAlerts();

  // Set up auto-refresh
  setInterval(() => {
    DataManager.refreshData();
    alertManager.refreshAlerts();
  }, UI_CONFIG.refreshInterval);
});
