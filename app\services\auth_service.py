"""
Authentication service for user management and JWT token handling.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
from app.models.schemas import User, UserInDB, TokenData
from app.core.config import get_settings

# Try to import authentication dependencies, fallback to basic implementation
try:
    from jose import JWTError, jwt
    from passlib.context import CryptContext
    JOSE_AVAILABLE = True
    PASSLIB_AVAILABLE = True
    # Password hashing context
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
except ImportError:
    JOSE_AVAILABLE = False
    PASSLIB_AVAILABLE = False
    print("⚠️  Authentication dependencies not installed. Using basic fallback authentication.")
    print("   Install with: pip install python-jose[cryptography] passlib[bcrypt]")

# Get settings
settings = get_settings()

# JWT settings
SECRET_KEY = getattr(settings, 'jwt_secret_key', "your-secret-key-change-this-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = getattr(settings, 'access_token_expire_minutes', 480)  # 8 hours

# Simple fallback password verification (NOT SECURE - for development only)
def simple_hash_password(password: str) -> str:
    """Simple password hashing fallback (NOT SECURE)."""
    import hashlib
    return hashlib.sha256(password.encode()).hexdigest()

def simple_verify_password(plain_password: str, hashed_password: str) -> bool:
    """Simple password verification fallback (NOT SECURE)."""
    if hashed_password.startswith("$2b$"):  # bcrypt hash
        return PASSLIB_AVAILABLE and pwd_context.verify(plain_password, hashed_password)
    else:  # simple hash
        return simple_hash_password(plain_password) == hashed_password

# In-memory user database (will be initialized after imports)
fake_users_db: Dict[str, UserInDB] = {}


class AuthService:
    """Authentication service for handling user authentication and JWT tokens."""

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a plain password against its hash."""
        if PASSLIB_AVAILABLE:
            return pwd_context.verify(plain_password, hashed_password)
        else:
            return simple_verify_password(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate password hash."""
        if PASSLIB_AVAILABLE:
            return pwd_context.hash(password)
        else:
            return simple_hash_password(password)

    @staticmethod
    def get_user(username: str) -> Optional[UserInDB]:
        """Get user from database by username."""
        return fake_users_db.get(username)

    @staticmethod
    def authenticate_user(username: str, password: str) -> Optional[UserInDB]:
        """Authenticate user with username and password."""
        user = AuthService.get_user(username)
        if not user:
            return None
        if not AuthService.verify_password(password, user.hashed_password):
            return None
        return user

    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token."""
        if not JOSE_AVAILABLE:
            # Fallback: create a simple token (NOT SECURE - for development only)
            import json
            import base64
            payload = {
                "sub": data.get("sub"),
                "exp": (datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))).timestamp()
            }
            token_data = json.dumps(payload).encode()
            return base64.b64encode(token_data).decode()

        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt

    @staticmethod
    def verify_token(token: str) -> Optional[TokenData]:
        """Verify JWT token and return token data."""
        if not JOSE_AVAILABLE:
            # Fallback: decode simple token (NOT SECURE - for development only)
            try:
                import json
                import base64
                token_data = json.loads(base64.b64decode(token.encode()).decode())
                username = token_data.get("sub")
                exp = token_data.get("exp")

                # Check expiration
                if exp and datetime.utcnow().timestamp() > exp:
                    return None

                if username is None:
                    return None
                return TokenData(username=username)
            except Exception:
                return None

        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            if username is None:
                return None
            token_data = TokenData(username=username)
            return token_data
        except JWTError:
            return None

    @staticmethod
    def get_current_user(token: str) -> Optional[User]:
        """Get current user from JWT token."""
        token_data = AuthService.verify_token(token)
        if token_data is None:
            return None
        
        user = AuthService.get_user(username=token_data.username)
        if user is None:
            return None
        
        return User(
            username=user.username,
            full_name=user.full_name,
            email=user.email,
            is_active=user.is_active
        )

    @staticmethod
    def add_user(username: str, password: str, full_name: Optional[str] = None, 
                 email: Optional[str] = None) -> UserInDB:
        """Add a new user to the database."""
        if username in fake_users_db:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
        
        hashed_password = AuthService.get_password_hash(password)
        user = UserInDB(
            username=username,
            full_name=full_name or username,
            email=email,
            hashed_password=hashed_password,
            is_active=True
        )
        fake_users_db[username] = user
        return user

    @staticmethod
    def change_password(username: str, old_password: str, new_password: str) -> bool:
        """Change user password."""
        user = AuthService.authenticate_user(username, old_password)
        if not user:
            return False
        
        new_hashed_password = AuthService.get_password_hash(new_password)
        fake_users_db[username].hashed_password = new_hashed_password
        return True

    @staticmethod
    def deactivate_user(username: str) -> bool:
        """Deactivate a user account."""
        if username not in fake_users_db:
            return False
        fake_users_db[username].is_active = False
        return True

    @staticmethod
    def activate_user(username: str) -> bool:
        """Activate a user account."""
        if username not in fake_users_db:
            return False
        fake_users_db[username].is_active = True
        return True

    @staticmethod
    def list_users() -> Dict[str, User]:
        """List all users (without passwords)."""
        return {
            username: User(
                username=user.username,
                full_name=user.full_name,
                email=user.email,
                is_active=user.is_active
            )
            for username, user in fake_users_db.items()
        }


# Initialize default users
def init_default_users():
    """Initialize default users if they don't exist."""
    global fake_users_db

    if not fake_users_db:  # Only initialize if empty
        fake_users_db = {
            "admin": UserInDB(
                username="admin",
                full_name="Administrator",
                email="<EMAIL>",
                hashed_password="$2b$12$wybryr2T18eRKoC5Flo11uVPK0EMrobnPA3zy9B8uSOzjgRJUU.a6" if PASSLIB_AVAILABLE else simple_hash_password("admin123"),  # "admin123"
                is_active=True
            ),
            "user": UserInDB(
                username="user",
                full_name="Regular User",
                email="<EMAIL>",
                hashed_password="$2b$12$yJ63G3zI43jR686rfL37qeFvZLywAyh23jwRlSOihdPl3fzgDhkLC" if PASSLIB_AVAILABLE else simple_hash_password("user123"),  # "user123"
                is_active=True
            ),
            # Add new users here following this pattern:
            # "newuser": UserInDB(
            #     username="newuser",
            #     full_name="New User Name",
            #     email="<EMAIL>",
            #     hashed_password="$2b$12$..." if PASSLIB_AVAILABLE else simple_hash_password("password"),
            #     is_active=True
            # ),
        }

# Initialize users when module is imported
init_default_users()


# Export the service instance
auth_service = AuthService()
