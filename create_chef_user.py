#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a chef user for testing role-based access control.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.auth_service import AuthService

def main():
    """Create a chef user for testing."""
    auth_service = AuthService()

    # Create chef user directly
    chef_data = {
        "username": "chef",
        "password": "chef123",
        "full_name": "Chef de Production",
        "email": "<EMAIL>",
        "role": "chef"
    }
    
    try:
        result = auth_service.add_user(
            username=chef_data["username"],
            password=chef_data["password"],
            full_name=chef_data["full_name"],
            email=chef_data["email"],
            role=chef_data["role"]
        )
        if result:
            print("✅ Chef user created successfully!")
            print(f"Username: chef")
            print(f"Password: chef123")
            print(f"Role: chef")
        else:
            print("❌ Failed to create chef user")
    except Exception as e:
        print(f"❌ Error creating chef user: {e}")

if __name__ == "__main__":
    main()
